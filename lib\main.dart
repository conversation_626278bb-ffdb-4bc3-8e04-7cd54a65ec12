import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:admin/widgets/login.dart';
import 'package:admin/src/config/app_localizations.dart';
import 'package:admin/src/config/theme_provider.dart'; // Adjust path if necessary
import 'package:admin/widgets/locale_provider.dart'; // Adjust path if necessary
import 'package:admin/widgets/dashboard.dart'; // Import DashboardApp

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => LocaleProvider()), // Add LocaleProvider
      ],
      child: Consumer<LocaleProvider>(
        builder: (context, localeProvider, child) {
          return MaterialApp(
            title: 'Login App',
            theme: ThemeData(primarySwatch: Colors.blue),
            home: const LoginApp(), // Start with LoginApp
            supportedLocales: const [
              Locale('en'),
              Locale('fr'),
              Locale('ar'),
            ],
            localizationsDelegates: const [
              AppLocalizations.delegate,
              ...GlobalMaterialLocalizations.delegates,
              GlobalWidgetsLocalizations.delegate,
            ],
            locale: localeProvider.locale, // Use LocaleProvider's locale
            localeResolutionCallback: (locale, supportedLocales) {
              for (var supportedLocale in supportedLocales) {
                if (supportedLocale.languageCode == locale?.languageCode) {
                  return supportedLocale;
                }
              }
              return const Locale('en'); // Default to English
            },
          );
        },
      ),
    );
  }
}