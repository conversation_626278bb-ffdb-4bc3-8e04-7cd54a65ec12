import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:convert';
import 'package:provider/provider.dart';
import 'dashboard.dart';
import 'locale_provider.dart';
import 'package:admin/src/config/theme_provider.dart';
import 'package:admin/src/config/app_localizations.dart';

class AppColors {
  static const Color primaryOrange = Color(0xFFFF5722);
  static const Color pureWhite = Color(0xFFFFFFFF);
  static const Color orangeLight = Color(0xFFFF8A65);
  static const Color orangePale = Color(0xFFFFEBE7);
  static const Color darkGray = Color(0xFF212121);
  static const Color mediumGray = Color(0xFF757575);
  static const Color lightGray = Color(0xFFF5F5F5);
  static const Color borderGray = Color(0xFFE0E0E0);
  static const Color success = Color(0xFF4CAF50);
  static const Color error = Color(0xFFD32F2F);
}

class LoginApp extends StatefulWidget {
  const LoginApp({super.key});

  @override
  _LoginAppState createState() => _LoginAppState();
}

class _LoginAppState extends State<LoginApp> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;
  final _secureStorage = const FlutterSecureStorage();

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  String? _validateUsername(String? value, BuildContext context) {
    final localizations = AppLocalizations.of(context);
    if (value == null || value.isEmpty) {
      return localizations?.translate('enter_username') ?? 'Enter your username';
    }
    return null;
  }

  String? _validatePassword(String? value, BuildContext context) {
    final localizations = AppLocalizations.of(context);
    if (value == null || value.isEmpty) {
      return localizations?.translate('enter_password') ?? 'Enter your password';
    }
    if (value.length < 6) {
      return localizations?.translate('password_min_length') ??
          'Password must be at least 6 characters long';
    }
    return null;
  }

  Future<void> _handleSignIn(BuildContext context) async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      try {
        final response = await http.post(
          Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/login'),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: jsonEncode({
            'email': _usernameController.text.trim(),
            'password': _passwordController.text,
            'type': 'admin',
          }),
        );

        print('Status Code: ${response.statusCode}');
        print('Response Body: ${response.body}');

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          final token = data['data']['token'] as String?;
          if (token != null) {
            await _secureStorage.write(key: 'auth_token', value: token);

            final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
            final userLocale = data['locale'] as String? ?? 'en';
            localeProvider.setLocale(Locale(userLocale));

            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => const DashboardApp()),
            );
          } else {
            setState(() {
              _errorMessage = AppLocalizations.of(context)
                      ?.translate('login_failed') ??
                  'Login failed: Token not found';
            });
          }
        } else {
          final decodedBody = jsonDecode(response.body);
          setState(() {
            _errorMessage = (decodedBody['message'] as String?) ??
                (AppLocalizations.of(context)?.translate('login_failed') ??
                    'Login failed (Status: ${response.statusCode})');
          });
        }
      } catch (e) {
        print('Error: $e');
        setState(() {
          _errorMessage = AppLocalizations.of(context)
                  ?.translate('network_error') ??
              'Network error';
        });
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isDesktop = MediaQuery.of(context).size.width >= 600;
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: AppColors.lightGray,
      body: Center(
        child: Container(
          width: isDesktop ? 450 : double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
          child: Card(
            elevation: 6,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            color: AppColors.pureWhite,
            child: Padding(
              padding: const EdgeInsets.all(32),
              child: Form(
                key: _formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.lock_outline,
                      size: 72,
                      color: AppColors.primaryOrange,
                    ),
                    const SizedBox(height: 24),
                    Text(
                      localizations?.translate('login') ?? 'Login',
                      style: const TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.w700,
                        color: AppColors.darkGray,
                      ),
                    ),
                    const SizedBox(height: 32),
                    TextFormField(
                      controller: _usernameController,
                      validator: (value) => _validateUsername(value, context),
                      decoration: InputDecoration(
                        labelText: localizations?.translate('username') ?? 'Username',
                        labelStyle: const TextStyle(color: AppColors.mediumGray),
                        filled: true,
                        fillColor: AppColors.orangePale,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: AppColors.borderGray),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: AppColors.primaryOrange, width: 2),
                        ),
                        prefixIcon: const Icon(Icons.person_outline, color: AppColors.primaryOrange),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _passwordController,
                      obscureText: true,
                      validator: (value) => _validatePassword(value, context),
                      decoration: InputDecoration(
                        labelText: localizations?.translate('password') ?? 'Password',
                        labelStyle: const TextStyle(color: AppColors.mediumGray),
                        filled: true,
                        fillColor: AppColors.orangePale,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: AppColors.borderGray),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: AppColors.primaryOrange, width: 2),
                        ),
                        prefixIcon: const Icon(Icons.lock_outline, color: AppColors.primaryOrange),
                      ),
                    ),
                    if (_errorMessage != null) ...[
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        style: const TextStyle(
                          color: AppColors.error,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryOrange,
                          foregroundColor: AppColors.pureWhite,
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                          elevation: 2,
                        ),
                        onPressed: _isLoading ? null : () => _handleSignIn(context),
                        child: _isLoading
                            ? const CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(AppColors.pureWhite),
                              )
                            : Text(
                                localizations?.translate('sign_in') ?? 'Sign In',
                                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                              ),
                      ),
                    ),
                    if (isDesktop) ...[
                      const SizedBox(height: 16),
                      TextButton(
                        onPressed: () {},
                        child: Text(
                          localizations?.translate('forgot_password') ?? 'Forgot Password?',
                          style: const TextStyle(color: AppColors.primaryOrange, fontSize: 14),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}