import 'package:flutter/material.dart';

class AppLocalizations {
  AppLocalizations(this.locale);

  final Locale locale;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  static const Map<String, Map<String, String>> _translations = {
    'fr': {
      'login': 'Connexion',
      'username': 'Nom d\'utilisateur',
      'enter_username': 'Entrez votre nom d\'utilisateur',
      'password': 'Mot de passe',
      'enter_password': 'Entrez votre mot de passe',
      'password_min_length': 'Le mot de passe doit contenir au moins 8 caractères',
      'login_failed': 'Échec de la connexion',
      'network_error': 'Erreur réseau',
      'forgot_password': 'Mot de passe oublié ?',
      'dashboard': '<PERSON>au de bord',
      'requests': 'Demandes',
      'recourse': 'Recours',
      'profile': 'Profil',
      'name': 'Nom',
      'email': 'Email',
      'role': '<PERSON><PERSON><PERSON>',
      'administrator': 'Administrateur',
      'logout': 'Déconnexion',
      'edit_profile': 'Modifier le profil',
      'sign_in': 'Se connecter',
      'overview': 'Aperçu',
      'appeals': 'Recours',
      'permits': 'Permis',
      'retry': 'Réessayer',
      'no_requests': 'Aucune demande trouvée',
      'no_appeals': 'Aucun recours trouvé',
      'no_permits': 'Aucun permis trouvé',
      'request': 'Demande',
      'approve': 'Approuver',
      'reject': 'Rejeter',
      'view_document': 'Voir le document',
      'appeal_for_request': 'Recours pour demande',
      'reason': 'Raison',
      'issued_at': 'Émis le',
      'expires_at': 'Expire le',
      'permit_document': 'Document de permis',
      'statistics': 'Statistiques',
      'total_requests': 'Total des demandes',
      'pending_requests': 'En attente',
      'approved_requests': 'Approuvées',
      'rejected_requests': 'Rejetées',
      'total_appeals': 'Total des recours',
      'pending_appeals': 'Recours en attente',
      'total_permits': 'Total des permis',
      'cancel': 'Annuler',
      'confirm': 'Confirmer',
    },
    'ar': {
      'login': 'تسجيل الدخول',
      'username': 'اسم المستخدم',
      'enter_username': 'أدخل اسم المستخدم',
      'password': 'كلمة المرور',
      'enter_password': 'أدخل كلمة المرور',
      'password_min_length': 'يجب أن تتكون كلمة المرور من 8 أحرف على الأقل',
      'login_failed': 'فشل تسجيل الدخول',
      'network_error': 'خطأ في الشبكة',
      'forgot_password': 'نسيت كلمة المرور؟',
      'dashboard': 'لوحة التحكم',
      'requests': 'الطلبات',
      'recourse': 'اللجوء',
      'profile': 'الملف الشخصي',
      'name': 'الاسم',
      'email': 'البريد الإلكتروني',
      'role': 'الدور',
      'administrator': 'مدير',
      'logout': 'تسجيل الخروج',
      'edit_profile': 'تعديل الملف الشخصي',
      'sign_in': 'تسجيل الدخول',
      'overview': 'نظرة عامة',
      'appeals': 'اللجوء',
      'permits': 'التصاريح',
      'retry': 'أعد المحاولة',
      'no_requests': 'لا توجد طلبات',
      'no_appeals': 'لا يوجد لجوء',
      'no_permits': 'لا يوجد تصاريح',
      'request': 'طلب',
      'approve': 'موافقة',
      'reject': 'رفض',
      'view_document': 'عرض الوثيقة',
      'appeal_for_request': 'لجوء للطلب',
      'reason': 'السبب',
      'issued_at': 'تم الإصدار في',
      'expires_at': 'ينتهي في',
      'permit_document': 'وثيقة التصريح',
      'statistics': 'إحصائيات',
      'total_requests': 'إجمالي الطلبات',
      'pending_requests': 'قيد الانتظار',
      'approved_requests': 'تمت الموافقة عليها',
      'rejected_requests': 'مرفوضة',
      'total_appeals': 'إجمالي اللجوء',
      'pending_appeals': 'لجوء قيد الانتظار',
      'total_permits': 'إجمالي التصاريح',
      'cancel': 'إلغاء',
      'confirm': 'تأكيد',
    },
    'en': {
      'login': 'Login',
      'username': 'Username',
      'enter_username': 'Enter your username',
      'password': 'Password',
      'enter_password': 'Enter your password',
      'password_min_length': 'Password must be at least 8 characters long',
      'login_failed': 'Login failed',
      'network_error': 'Network error',
      'forgot_password': 'Forgot Password?',
      'dashboard': 'Dashboard',
      'requests': 'Requests',
      'recourse': 'Recourse',
      'profile': 'Profile',
      'name': 'Name',
      'email': 'Email',
      'role': 'Role',
      'administrator': 'Administrator',
      'logout': 'Logout',
      'edit_profile': 'Edit Profile',
      'sign_in': 'Sign In',
      'overview': 'Overview',
      'appeals': 'Appeals',
      'permits': 'Permits',
      'retry': 'Retry',
      'no_requests': 'No requests found',
      'no_appeals': 'No appeals found',
      'no_permits': 'No permits found',
      'request': 'Request',
      'approve': 'Approve',
      'reject': 'Reject',
      'view_document': 'View Document',
      'appeal_for_request': 'Appeal for request',
      'reason': 'Reason',
      'issued_at': 'Issued at',
      'expires_at': 'Expires at',
      'permit_document': 'Permit Document',
      'statistics': 'Statistics',
      'total_requests': 'Total Requests',
      'pending_requests': 'Pending',
      'approved_requests': 'Approved',
      'rejected_requests': 'Rejected',
      'total_appeals': 'Total Appeals',
      'pending_appeals': 'Pending Appeals',
      'total_permits': 'Total Permits',
      'cancel': 'Cancel',
      'confirm': 'Confirm',
    },
  };

  String translate(String key) {
    final translations = _translations[locale.languageCode];
    if (translations != null && translations.containsKey(key)) {
      return translations[key]!;
    }
    final fallback = _translations['en'];
    if (fallback != null && fallback.containsKey(key)) {
      return fallback[key]!;
    }
    return key; // Fallback to key itself if no translation found
  }
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) => ['en', 'fr', 'ar'].contains(locale.languageCode);

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}